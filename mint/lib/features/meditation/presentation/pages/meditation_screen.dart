import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:lottie/lottie.dart';
import 'package:mental_health/core/services/firebase_data_seeder.dart';
import 'package:mental_health/core/theme.dart';
import 'package:mental_health/features/meditation/presentation/bloc/daily_quote/daily_quote_bloc.dart';
import 'package:mental_health/features/meditation/presentation/bloc/daily_quote/daily_quote_event.dart';
import 'package:mental_health/features/meditation/presentation/bloc/daily_quote/daily_quote_state.dart';
import 'package:mental_health/features/meditation/presentation/bloc/mood_messenge/mood_messenge_bloc.dart';
import 'package:mental_health/features/meditation/presentation/bloc/mood_messenge/mood_messenge_event.dart';
import 'package:mental_health/features/meditation/presentation/bloc/mood_messenge/mood_messenge_state.dart';

import 'package:mental_health/features/meditation/presentation/widgets/feeling_button.dart';
import 'package:mental_health/features/meditation/presentation/widgets/mini_task_card.dart';

class MeditationScreen extends StatefulWidget {
  const MeditationScreen({Key? key}) : super(key: key);

  @override
  State<MeditationScreen> createState() => _MeditationScreenState();
}

class _MeditationScreenState extends State<MeditationScreen> {
  // Track completion status for each task
  bool _morningCompleted = false;
  bool _noonCompleted = false;
  bool _eveningCompleted = false;

  void _showTaskCompletionFeedback(String taskName, bool isCompleted) {
    final message = isCompleted
        ? 'Tuyệt vời! Bạn đã hoàn thành nhiệm vụ $taskName 🎉'
        : 'Nhiệm vụ $taskName đã được đánh dấu chưa hoàn thành';

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor:
            isCompleted ? Colors.green.shade600 : Colors.orange.shade600,
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }

  void _showTaskDetails(String taskName, String description) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: [
            Icon(
              _getTaskIcon(taskName),
              color: _getTaskIconColor(taskName),
              size: 28,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                taskName,
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Chi tiết nhiệm vụ:',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: Colors.grey.shade700,
                  ),
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey.shade200),
              ),
              child: Text(
                description,
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      height: 1.5,
                    ),
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Icon(
                  Icons.schedule,
                  size: 16,
                  color: Colors.grey.shade600,
                ),
                const SizedBox(width: 8),
                Text(
                  _getTaskTimeRange(taskName),
                  style: TextStyle(
                    color: Colors.grey.shade600,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Đóng'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Có thể thêm logic để đánh dấu hoàn thành từ đây
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: _getTaskIconColor(taskName),
              foregroundColor: DefaultColors.primary,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text('Bắt đầu'),
          ),
        ],
      ),
    );
  }

  IconData _getTaskIcon(String taskName) {
    switch (taskName.toLowerCase()) {
      case 'buổi sáng':
        return Icons.wb_sunny;
      case 'buổi trưa':
        return Icons.wb_sunny_outlined;
      case 'buổi tối':
        return Icons.nightlight_round;
      default:
        return Icons.schedule;
    }
  }

  Color _getTaskIconColor(String taskName) {
    switch (taskName.toLowerCase()) {
      case 'buổi sáng':
        return Colors.orange.shade600;
      case 'buổi trưa':
        return Colors.amber.shade700;
      case 'buổi tối':
        return Colors.indigo.shade600;
      default:
        return Colors.grey.shade600;
    }
  }

  String _getTaskTimeRange(String taskName) {
    switch (taskName.toLowerCase()) {
      case 'buổi sáng':
        return 'Thời gian khuyến nghị: 6:00 - 12:00';
      case 'buổi trưa':
        return 'Thời gian khuyến nghị: 12:00 - 18:00';
      case 'buổi tối':
        return 'Thời gian khuyến nghị: 18:00 - 22:00';
      default:
        return 'Cả ngày';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent, // Để nền trong suốt
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.transparent,
        leadingWidth: 100, // AppBar trong suốt
        actions: [
          Row(
            spacing: 15,
            children: [
              Row(
                spacing: 5,
                children: [
                  Container(
                    height: 24,
                    padding: const EdgeInsets.only(right: 6),
                    decoration: BoxDecoration(
                        // borderRadius: BorderRadius.all(Radius.circular(3)),
                        // color: DefaultColors.primary,
                        gradient: LinearGradient(colors: [
                      DefaultColors.primary.withValues(alpha: 0.01),
                      DefaultColors.primary.withValues(alpha: 0.1),
                      DefaultColors.primary.withValues(alpha: 0.4),
                      DefaultColors.primary.withValues(alpha: 0.6),
                      DefaultColors.primary.withValues(alpha: 0.7),
                    ])),
                    child: Row(spacing: 5, children: [
                      SizedBox(
                          width: 20,
                          child: Image.asset('assets/icons/flame.png')),
                      Text('100',
                          style: TextPresets.bodyMedium.copyWith(
                            color: Colors.black,
                            fontWeight: FontWeight.w900,
                          )),
                    ]),
                  ),
                  SizedBox(
                    width: 2,
                  ),
                  Container(
                    height: 24,
                    padding: const EdgeInsets.only(right: 6),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(5),
                        bottomLeft: Radius.circular(5),
                      ),
                      gradient: LinearGradient(colors: [
                        DefaultColors.primary.withValues(alpha: 0.001),
                        DefaultColors.primary.withValues(alpha: 0.2),
                        DefaultColors.primary.withValues(alpha: 0.5),
                        DefaultColors.primary.withValues(alpha: 0.6),
                        DefaultColors.primary.withValues(alpha: 0.7),
                      ]),
                    ),
                    child: Row(spacing: 6, children: [
                      SizedBox(
                          width: 24,
                          child: Image.asset('assets/icons/coin.png')),
                      Text(
                        '100000',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.black,
                          fontWeight: FontWeight.w900,
                        ),
                      ),
                    ]),
                  ),
                  Container(
                    height: 24,
                    padding: const EdgeInsets.all(3),
                    decoration: BoxDecoration(
                      color: const Color.fromARGB(255, 4, 112, 0)
                          .withValues(alpha: 0.7),
                      borderRadius: BorderRadius.only(
                        topRight: Radius.circular(5),
                        bottomRight: Radius.circular(5),
                      ),
                    ),
                    child: Icon(
                      Icons.add,
                      color: DefaultColors.textPrimary,
                      weight: 15,
                      size: 18,
                    ),
                  )
                ],
              ),
              CircleAvatar(
                backgroundImage: AssetImage('assets/images/profile.png'),
              ),
              SizedBox(
                width: 5,
              ),
            ],
          ),
        ],
      ),
      body: Container(
        color: Colors.transparent,
        padding: const EdgeInsets.all(16),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Chào mừng trở lại, Akuro!', style: TextPresets.title),
              SizedBox(
                height: 32,
              ),
              Text(
                'Lõi cảm xúc',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: Colors.black,
                      fontWeight: FontWeight.bold,
                    ),
              ),
              SizedBox(
                height: 16,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  FeelingButton(
                    feeling: 'Mục tiêu',
                    process: 0.8,
                    color: DefaultColors.dopamine,
                    icon: 'assets/icons/target.svg',
                    colorLight: DefaultColors.dopamineNeon,
                  ),
                  FeelingButton(
                    feeling: 'Vận động',
                    process: 0.6,
                    color: DefaultColors.endorphin,
                    icon: 'assets/icons/energy.svg',
                    colorLight: DefaultColors.endorphinNeon,
                  ),
                  FeelingButton(
                    feeling: 'Gắn kết',
                    process: 0.3,
                    color: DefaultColors.oxytocin,
                    icon: 'assets/icons/heart.svg',
                    colorLight: DefaultColors.oxytocinNeon,
                  ),
                  FeelingButton(
                    feeling: 'Tự tin',
                    process: 0.9,
                    color: DefaultColors.serotonin,
                    icon: 'assets/icons/galaxy-star.svg',
                    colorLight: DefaultColors.serotoninNeon,
                  ),
                ],
              ),
              SizedBox(
                height: 24,
              ),
              Text(
                'Nhiệm vụ hôm nay',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: Colors.black,
                      fontWeight: FontWeight.bold,
                    ),
              ),
              SizedBox(
                height: 8,
              ),
              TaskCard(
                  title: 'Thực hành biết ơn',
                  description: 'Viết ra 3 điều biết ơn trong ngày hôm nay'),
              SizedBox(
                height: 8,
              ),
              TaskCard(
                  title: 'Thực hành biết ơn',
                  description: 'Viết ra 3 điều biết ơn trong ngày hôm nay'),
              SizedBox(
                height: 8,
              ),
              TaskCard(
                  title: 'Thực hành biết ơn',
                  description: 'Viết ra 3 điều biết ơn trong ngày hôm nay'),
              SizedBox(
                height: 24,
              ),
              Text(
                'Chức năng khác',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              SizedBox(
                height: 8,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  Column(
                    children: [
                      Container(
                        width: 60,
                        height: 60,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          color: DefaultColors.reversePrimary.withAlpha(160),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.05),
                              blurRadius: 10,
                              spreadRadius: 2,
                            ),
                          ],
                        ),
                        child: Icon(
                          Icons.settings,
                          color: DefaultColors.reverseTextPrimary,
                          size: 30,
                        ),
                      ),
                      Text(
                        'Cài đặt',
                        style: TextStyle(
                          color: DefaultColors.reverseTextPrimary,
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                  Column(
                    children: [
                      Container(
                        width: 60,
                        height: 60,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          color: DefaultColors.reversePrimary.withAlpha(160),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.05),
                              blurRadius: 10,
                              spreadRadius: 2,
                            ),
                          ],
                        ),
                        child: Icon(
                          Icons.settings,
                          color: DefaultColors.reverseTextPrimary,
                          size: 30,
                        ),
                      ),
                      Text(
                        'Cài đặt',
                        style: TextStyle(
                          color: DefaultColors.reverseTextPrimary,
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                  Column(
                    children: [
                      Container(
                        width: 60,
                        height: 60,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          color: DefaultColors.reversePrimary.withAlpha(160),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.05),
                              blurRadius: 10,
                              spreadRadius: 2,
                            ),
                          ],
                        ),
                        child: Icon(
                          Icons.settings,
                          color: DefaultColors.reverseTextPrimary,
                          size: 30,
                        ),
                      ),
                      Text(
                        'Cài đặt',
                        style: TextStyle(
                          color: DefaultColors.reverseTextPrimary,
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                  Column(
                    children: [
                      Container(
                        width: 60,
                        height: 60,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          color: DefaultColors.reversePrimary.withAlpha(160),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.05),
                              blurRadius: 10,
                              spreadRadius: 2,
                            ),
                          ],
                        ),
                        child: Icon(
                          Icons.settings,
                          color: DefaultColors.reverseTextPrimary,
                          size: 30,
                        ),
                      ),
                      Text(
                        'Cài đặt',
                        style: TextStyle(
                          color: DefaultColors.reverseTextPrimary,
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ],
              )
            ],
          ),
        ),
      ),
    );
  }
}
